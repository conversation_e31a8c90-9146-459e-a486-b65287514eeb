#include <iostream>
using namespace std;

void pattern13(int n)
{
    for (int i = 1; i <= n; i++)
    {
        int space = 2 * (n - i);

        // increasing numbers
        for (int j = 1; j <= i; j++)
        {
            cout << j;
        }

        // spaces
        for (int j = 1; j <= space; j++)
        {
            cout << " ";
        }

        // decreasing numbers
        for (int j = i; j >= 1; j--)
        {
            cout << j;
        }

        cout << endl;
    }
}

int main()
{
    pattern13(5);
    return 0;
}
