#include <iostream>
using namespace std;

void pattern9(int n)
{
    // Top half of the diamond
    for(int i=0; i<n; i++)
    {
        for(int j=0; j<n-i-1; j++)
        {
            cout<<" ";
        }
        for(int j=0; j<2*i+1; j++)
        {
            cout<<"*";
        }
        cout<<endl;
    }

    // Bottom half of the diamond
    for(int i=0; i<n; i++)
    {
        for(int j=0; j<=i; j++)
        {
            cout<< " ";
        }
        for(int j=0; j<2*n-(2*i+1); j++)
        {
            cout<<"*";
        }
        cout<<endl;
    }
}

int main()
{
    pattern9(4);
    return 0;
}
