#include <iostream>
#include <vector>
#include <bits/stdc++.h>
using namespace std;
int main()
{
    vector<int>v={1,2,3,4,5};
    v.emplace_back(6);
    for(auto i : v)
    {
        cout<<i<<" ";
    }
    cout<<endl;

    vector<pair<int,int>>v1={{1,2},{3,4},{5,6}};

    for(auto i : v1)
    {
        cout<<i.first<<" "<<i.second<<endl;
    }
    
    vector<int>v2={1,2,3,4,5};

for(vector<int>::iterator it = v2.begin();it!=v2.end();it++)
{
    cout<<*it<<" ";
}
cout<<endl;
for(auto it = v2.begin();it!=v2.end();it++)
{
    cout<<*it<<" ";
}


return 0;
}